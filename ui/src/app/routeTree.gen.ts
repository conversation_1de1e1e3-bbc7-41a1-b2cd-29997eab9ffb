/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root';
import { Route as VolumesRouteImport } from './routes/volumes';
import { Route as HomeRouteImport } from './routes/home';
import { Route as IndexRouteImport } from './routes/index';
import { Route as QueriesIndexRouteImport } from './routes/queries/index';
import { Route as DatabasesIndexRouteImport } from './routes/databases/index';
import { Route as SqlEditorWorksheetIdIndexRouteImport } from './routes/sql-editor/$worksheetId.index';
import { Route as QueriesQueryIdIndexRouteImport } from './routes/queries/$queryId.index';
import { Route as DatabasesDatabaseNameSchemasIndexRouteImport } from './routes/databases/$databaseName.schemas.index';
import { Route as DatabasesDatabaseNameSchemasSchemaNameTablesIndexRouteImport } from './routes/databases/$databaseName.schemas.$schemaName.tables.index';
import { Route as DatabasesDatabaseNameSchemasSchemaNameTablesTableNameColumnsIndexRouteImport } from './routes/databases/$databaseName.schemas.$schemaName.tables.$tableName.columns.index';

const VolumesRoute = VolumesRouteImport.update({
  id: '/volumes',
  path: '/volumes',
  getParentRoute: () => rootRouteImport,
} as any);
const HomeRoute = HomeRouteImport.update({
  id: '/home',
  path: '/home',
  getParentRoute: () => rootRouteImport,
} as any);
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any);
const QueriesIndexRoute = QueriesIndexRouteImport.update({
  id: '/queries/',
  path: '/queries/',
  getParentRoute: () => rootRouteImport,
} as any);
const DatabasesIndexRoute = DatabasesIndexRouteImport.update({
  id: '/databases/',
  path: '/databases/',
  getParentRoute: () => rootRouteImport,
} as any);
const SqlEditorWorksheetIdIndexRoute =
  SqlEditorWorksheetIdIndexRouteImport.update({
    id: '/sql-editor/$worksheetId/',
    path: '/sql-editor/$worksheetId/',
    getParentRoute: () => rootRouteImport,
  } as any);
const QueriesQueryIdIndexRoute = QueriesQueryIdIndexRouteImport.update({
  id: '/queries/$queryId/',
  path: '/queries/$queryId/',
  getParentRoute: () => rootRouteImport,
} as any);
const DatabasesDatabaseNameSchemasIndexRoute =
  DatabasesDatabaseNameSchemasIndexRouteImport.update({
    id: '/databases/$databaseName/schemas/',
    path: '/databases/$databaseName/schemas/',
    getParentRoute: () => rootRouteImport,
  } as any);
const DatabasesDatabaseNameSchemasSchemaNameTablesIndexRoute =
  DatabasesDatabaseNameSchemasSchemaNameTablesIndexRouteImport.update({
    id: '/databases/$databaseName/schemas/$schemaName/tables/',
    path: '/databases/$databaseName/schemas/$schemaName/tables/',
    getParentRoute: () => rootRouteImport,
  } as any);
const DatabasesDatabaseNameSchemasSchemaNameTablesTableNameColumnsIndexRoute =
  DatabasesDatabaseNameSchemasSchemaNameTablesTableNameColumnsIndexRouteImport.update(
    {
      id: '/databases/$databaseName/schemas/$schemaName/tables/$tableName/columns/',
      path: '/databases/$databaseName/schemas/$schemaName/tables/$tableName/columns/',
      getParentRoute: () => rootRouteImport,
    } as any,
  );

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute;
  '/home': typeof HomeRoute;
  '/volumes': typeof VolumesRoute;
  '/databases': typeof DatabasesIndexRoute;
  '/queries': typeof QueriesIndexRoute;
  '/queries/$queryId': typeof QueriesQueryIdIndexRoute;
  '/sql-editor/$worksheetId': typeof SqlEditorWorksheetIdIndexRoute;
  '/databases/$databaseName/schemas': typeof DatabasesDatabaseNameSchemasIndexRoute;
  '/databases/$databaseName/schemas/$schemaName/tables': typeof DatabasesDatabaseNameSchemasSchemaNameTablesIndexRoute;
  '/databases/$databaseName/schemas/$schemaName/tables/$tableName/columns': typeof DatabasesDatabaseNameSchemasSchemaNameTablesTableNameColumnsIndexRoute;
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute;
  '/home': typeof HomeRoute;
  '/volumes': typeof VolumesRoute;
  '/databases': typeof DatabasesIndexRoute;
  '/queries': typeof QueriesIndexRoute;
  '/queries/$queryId': typeof QueriesQueryIdIndexRoute;
  '/sql-editor/$worksheetId': typeof SqlEditorWorksheetIdIndexRoute;
  '/databases/$databaseName/schemas': typeof DatabasesDatabaseNameSchemasIndexRoute;
  '/databases/$databaseName/schemas/$schemaName/tables': typeof DatabasesDatabaseNameSchemasSchemaNameTablesIndexRoute;
  '/databases/$databaseName/schemas/$schemaName/tables/$tableName/columns': typeof DatabasesDatabaseNameSchemasSchemaNameTablesTableNameColumnsIndexRoute;
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport;
  '/': typeof IndexRoute;
  '/home': typeof HomeRoute;
  '/volumes': typeof VolumesRoute;
  '/databases/': typeof DatabasesIndexRoute;
  '/queries/': typeof QueriesIndexRoute;
  '/queries/$queryId/': typeof QueriesQueryIdIndexRoute;
  '/sql-editor/$worksheetId/': typeof SqlEditorWorksheetIdIndexRoute;
  '/databases/$databaseName/schemas/': typeof DatabasesDatabaseNameSchemasIndexRoute;
  '/databases/$databaseName/schemas/$schemaName/tables/': typeof DatabasesDatabaseNameSchemasSchemaNameTablesIndexRoute;
  '/databases/$databaseName/schemas/$schemaName/tables/$tableName/columns/': typeof DatabasesDatabaseNameSchemasSchemaNameTablesTableNameColumnsIndexRoute;
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath;
  fullPaths:
    | '/'
    | '/home'
    | '/volumes'
    | '/databases'
    | '/queries'
    | '/queries/$queryId'
    | '/sql-editor/$worksheetId'
    | '/databases/$databaseName/schemas'
    | '/databases/$databaseName/schemas/$schemaName/tables'
    | '/databases/$databaseName/schemas/$schemaName/tables/$tableName/columns';
  fileRoutesByTo: FileRoutesByTo;
  to:
    | '/'
    | '/home'
    | '/volumes'
    | '/databases'
    | '/queries'
    | '/queries/$queryId'
    | '/sql-editor/$worksheetId'
    | '/databases/$databaseName/schemas'
    | '/databases/$databaseName/schemas/$schemaName/tables'
    | '/databases/$databaseName/schemas/$schemaName/tables/$tableName/columns';
  id:
    | '__root__'
    | '/'
    | '/home'
    | '/volumes'
    | '/databases/'
    | '/queries/'
    | '/queries/$queryId/'
    | '/sql-editor/$worksheetId/'
    | '/databases/$databaseName/schemas/'
    | '/databases/$databaseName/schemas/$schemaName/tables/'
    | '/databases/$databaseName/schemas/$schemaName/tables/$tableName/columns/';
  fileRoutesById: FileRoutesById;
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute;
  HomeRoute: typeof HomeRoute;
  VolumesRoute: typeof VolumesRoute;
  DatabasesIndexRoute: typeof DatabasesIndexRoute;
  QueriesIndexRoute: typeof QueriesIndexRoute;
  QueriesQueryIdIndexRoute: typeof QueriesQueryIdIndexRoute;
  SqlEditorWorksheetIdIndexRoute: typeof SqlEditorWorksheetIdIndexRoute;
  DatabasesDatabaseNameSchemasIndexRoute: typeof DatabasesDatabaseNameSchemasIndexRoute;
  DatabasesDatabaseNameSchemasSchemaNameTablesIndexRoute: typeof DatabasesDatabaseNameSchemasSchemaNameTablesIndexRoute;
  DatabasesDatabaseNameSchemasSchemaNameTablesTableNameColumnsIndexRoute: typeof DatabasesDatabaseNameSchemasSchemaNameTablesTableNameColumnsIndexRoute;
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/volumes': {
      id: '/volumes';
      path: '/volumes';
      fullPath: '/volumes';
      preLoaderRoute: typeof VolumesRouteImport;
      parentRoute: typeof rootRouteImport;
    };
    '/home': {
      id: '/home';
      path: '/home';
      fullPath: '/home';
      preLoaderRoute: typeof HomeRouteImport;
      parentRoute: typeof rootRouteImport;
    };
    '/': {
      id: '/';
      path: '/';
      fullPath: '/';
      preLoaderRoute: typeof IndexRouteImport;
      parentRoute: typeof rootRouteImport;
    };
    '/queries/': {
      id: '/queries/';
      path: '/queries';
      fullPath: '/queries';
      preLoaderRoute: typeof QueriesIndexRouteImport;
      parentRoute: typeof rootRouteImport;
    };
    '/databases/': {
      id: '/databases/';
      path: '/databases';
      fullPath: '/databases';
      preLoaderRoute: typeof DatabasesIndexRouteImport;
      parentRoute: typeof rootRouteImport;
    };
    '/sql-editor/$worksheetId/': {
      id: '/sql-editor/$worksheetId/';
      path: '/sql-editor/$worksheetId';
      fullPath: '/sql-editor/$worksheetId';
      preLoaderRoute: typeof SqlEditorWorksheetIdIndexRouteImport;
      parentRoute: typeof rootRouteImport;
    };
    '/queries/$queryId/': {
      id: '/queries/$queryId/';
      path: '/queries/$queryId';
      fullPath: '/queries/$queryId';
      preLoaderRoute: typeof QueriesQueryIdIndexRouteImport;
      parentRoute: typeof rootRouteImport;
    };
    '/databases/$databaseName/schemas/': {
      id: '/databases/$databaseName/schemas/';
      path: '/databases/$databaseName/schemas';
      fullPath: '/databases/$databaseName/schemas';
      preLoaderRoute: typeof DatabasesDatabaseNameSchemasIndexRouteImport;
      parentRoute: typeof rootRouteImport;
    };
    '/databases/$databaseName/schemas/$schemaName/tables/': {
      id: '/databases/$databaseName/schemas/$schemaName/tables/';
      path: '/databases/$databaseName/schemas/$schemaName/tables';
      fullPath: '/databases/$databaseName/schemas/$schemaName/tables';
      preLoaderRoute: typeof DatabasesDatabaseNameSchemasSchemaNameTablesIndexRouteImport;
      parentRoute: typeof rootRouteImport;
    };
    '/databases/$databaseName/schemas/$schemaName/tables/$tableName/columns/': {
      id: '/databases/$databaseName/schemas/$schemaName/tables/$tableName/columns/';
      path: '/databases/$databaseName/schemas/$schemaName/tables/$tableName/columns';
      fullPath: '/databases/$databaseName/schemas/$schemaName/tables/$tableName/columns';
      preLoaderRoute: typeof DatabasesDatabaseNameSchemasSchemaNameTablesTableNameColumnsIndexRouteImport;
      parentRoute: typeof rootRouteImport;
    };
  }
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  HomeRoute: HomeRoute,
  VolumesRoute: VolumesRoute,
  DatabasesIndexRoute: DatabasesIndexRoute,
  QueriesIndexRoute: QueriesIndexRoute,
  QueriesQueryIdIndexRoute: QueriesQueryIdIndexRoute,
  SqlEditorWorksheetIdIndexRoute: SqlEditorWorksheetIdIndexRoute,
  DatabasesDatabaseNameSchemasIndexRoute:
    DatabasesDatabaseNameSchemasIndexRoute,
  DatabasesDatabaseNameSchemasSchemaNameTablesIndexRoute:
    DatabasesDatabaseNameSchemasSchemaNameTablesIndexRoute,
  DatabasesDatabaseNameSchemasSchemaNameTablesTableNameColumnsIndexRoute:
    DatabasesDatabaseNameSchemasSchemaNameTablesTableNameColumnsIndexRoute,
};
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>();
