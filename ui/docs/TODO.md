# TODO

- [ ] sidebar-secondary-accent variable -> make it global
- [ ] bg-[#1F1F1F]
- [ ] Check hardcode pixels
- [ ] Tabs2 component
- [ ] Hardcode pixels
- [ ] Add File names & folders convention (https://github.com/alan2207/bulletproof-react/blob/master/docs/project-standards.md)
- [ ] Typography component
- [ ] Cleanup shadcn components (Remove unused code / variants / props)
- [ ] Remove isMobile hook and related packages
- [ ] Mutate vs MutateAsync
- [ ] isLoading vs isFetching (Fix naming everywhere)
- [ ] Loading states for QueryDetails and Home Pages
- [ ] Scrollbars usages
- [ ] Consistent table height (Not the same in Queries vs Query page)
- [ ] Handle empty due to search state
